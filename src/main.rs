#![allow(dead_code)]

mod interpreter;
mod machine_code_translator;
mod macho_parser;
mod parser;
mod translator;

use anyhow::{anyhow, Result};
use clap::{Args, Parser, Subcommand};
use std::fs;

use machine_code_translator::{insert_nopcode, read_nopcode};
use translator::{Mapping, Token};

// TODOs:
// Remove (most) printlns/dbgs from files
// Implement asm functionality
// Implement non-ARM64 ISAs
// Fix ALL linter warnings

#[derive(Debug, Parser)]
#[command(name = "nopc")]
#[command(about = "assorted NOPCode utilities", long_about = None)]
struct Cli {
    /// Custom mapping options
    #[clap(flatten)]
    custom_map: Option<MappingArg>,
    #[command(subcommand)]
    command: Commands,
}

#[derive(Clone, Debug, Args)]
struct MappingArg {
    #[arg(long, short = 's', global = true, help_heading = "Mapping Options")]
    space: Option<String>,
    #[arg(long, short = 't', global = true, help_heading = "Mapping Options")]
    tab: Option<String>,
    #[arg(long, short = 'l', global = true, help_heading = "Mapping Options")]
    line_feed: Option<String>,
}

#[derive(Debug, Subcommand)]
enum Commands {
    /// Executes NOPCode program
    #[command(arg_required_else_help = true)]
    Run {
        /// The ASM/MC program
        #[clap(flatten)]
        in_file: RunnableCode,
    },
    /// Translates NOPCode -> Whitespace and saves to file
    #[command(arg_required_else_help = true)]
    Translate {
        /// The file to read
        #[arg(required = true)]
        file: String,
        /// The output file's path
        out_path: Option<String>,
    },
    /// Creates NOPCode file, using Assembly/Machine Code, and a Whitespace program
    #[command(arg_required_else_help = true)]
    Create {
        /// The Whitespace program
        #[arg(required = true)]
        ws_file: String,
        /// The ASM/MC program
        #[clap(flatten)]
        in_file: LowLevelCode,
        /// The output file's path
        out_path: Option<String>,
    },
}

#[derive(Clone, Debug, Args)]
#[group(required = true, multiple = false)]
pub struct LowLevelCode {
    #[clap(short, long)]
    assembly_file: Option<String>,
    #[clap(short, long)]
    machine_code_file: Option<String>,
}

#[derive(Clone, Debug, Args)]
#[group(required = true, multiple = false)]
pub struct RunnableCode {
    #[clap(short, long)]
    assembly_file: Option<String>,
    #[clap(short, long)]
    machine_code_file: Option<String>,
    #[clap(short, long)]
    whitespace_file: Option<String>,
}

enum RunnableCodeType {
    Assembly(String),
    MachineCode(String),
    Whitespace(String),
}

impl RunnableCode {
    fn to_enum(&self) -> RunnableCodeType {
        if let Some(i) = &self.assembly_file {
            RunnableCodeType::Assembly(i.to_string())
        } else if let Some(i) = &self.machine_code_file {
            RunnableCodeType::MachineCode(i.to_string())
        } else if let Some(i) = &self.whitespace_file {
            RunnableCodeType::Whitespace(i.to_string())
        } else {
            panic!("not enough args supplied!")
        }
    }
}

fn main() -> Result<()> {
    let args = Cli::parse();

    // generate mapping from args
    let map;

    match args.custom_map {
        Some(mapping) => map = mapping,
        None => {
            map = MappingArg {
                space: None,
                tab: None,
                line_feed: None,
            }
        }
    }

    match args.command {
        Commands::Run { in_file } => {
            let mp = Mapping::from_args(Some(in_file.clone()), &map).unwrap();

            let file_name = match in_file.to_enum() {
                RunnableCodeType::Assembly(name) => name,
                RunnableCodeType::MachineCode(name) => name,
                RunnableCodeType::Whitespace(name) => name,
            };

            // translate -> parse -> interpret
            println!("translating {file_name}");
            // should work regardless of file type?
            let tokens = translator::translate_file(&file_name, mp, in_file.to_enum())?;
            println!("parsing {} tokens", tokens.len());
            let (instructions, _) = parser::parse(tokens)?;
            println!("executing {} instructions", instructions.len());
            interpreter::execute(instructions)?;
        }
        Commands::Translate { file, out_path } => {
            // Translate from nopcode -> WS
            // TODO switch to from_args()
            let mp = Mapping::from_code_type(RunnableCodeType::MachineCode(String::new()));

            let buffer = fs::read(&file)?;

            let tokens: Vec<Token> = read_nopcode(buffer, mp)?;

            // create whitespace map
            let ws_map: Mapping =
                Mapping::from_code_type(RunnableCodeType::Whitespace(String::new()));

            let write_ws: String = ws_map.map_back(tokens);

            let out_path_value;
            match out_path {
                Some(path) => out_path_value = path,
                None => out_path_value = file.clone() + ".ws",
            }

            fs::write(out_path_value, write_ws).expect("Unable to write file");
        }
        Commands::Create {
            ws_file,
            in_file,
            out_path,
        } => {
            // Translate from ASM + WS -> NOPCode

            // Assume machine code file for now
            // TODO swtich to from_args()
            let mp = Mapping::from_code_type(RunnableCodeType::MachineCode(String::new()));

            // create whitespace map
            let ws_map: Mapping =
                Mapping::from_code_type(RunnableCodeType::Whitespace(String::new()));

            // translate -> insert
            println!("translating {ws_file}");
            // always binary
            let tokens = translator::translate_file(
                &ws_file,
                ws_map,
                RunnableCodeType::Whitespace(String::new()),
            )?;

            let bin_file_name = in_file.machine_code_file.ok_or(anyhow!("ASM not supported (for now)"))?;
            let mut buffer = fs::read(&bin_file_name)?;

            insert_nopcode(&mut buffer, mp, tokens)?;

            let out_path_value;
            match out_path {
                Some(path) => out_path_value = path,
                None => out_path_value = bin_file_name + ".nopc",
            }

            fs::write(out_path_value, buffer).expect("Unable to write file");
        }
    }

    Ok(())
}
