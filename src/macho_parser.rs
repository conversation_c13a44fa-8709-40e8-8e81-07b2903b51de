use anyhow::{anyhow, Result};

use crate::machine_code_translator::{to_bytes, reverse_endian_fb};

/// Working with ARM64 ISA, instructions are 4 bytes
const INSTR_LEN: usize = 4;

struct ParserArgs<'a> {
    buffer: &'a mut [u8],
    offset: &'a mut usize,
    insertions: usize,
    com_size: u32,
    modify_offsets: &'a mut bool,
}

impl<'a> ParserArgs<'a> {
    fn unwrap(&mut self) -> (&mut [u8], &mut usize,
                         usize, u32, &mut bool) {
        (self.buffer, self.offset, self.insertions,
         self.com_size, self.modify_offsets)
    }
}

#[allow(non_camel_case_types)]
enum LoadCommand {
    LC_SYMTAB = 0x00000002,
    LC_DYSYMTAB = 0x0000000B,
    LC_LOAD_DYLIB = 0x0000000C,
    LC_LOAD_DYLINKER = 0x0000000E,
    LC_SEGMENT_64 = 0x00000019,
    LC_UUID = 0x0000001B,
    LC_CODE_SIGNATURE = 0x0000001D,
    LC_FUNCTION_STARTS = 0x00000026,
    LC_MAIN = 0x80000028,
    LC_DATA_IN_CODE = 0x00000029,
    LC_SOURCE_VERSION = 0x0000002A,
    LC_BUILD_VERSION = 0x00000032,
    LC_DYLD_EXPORTS_TRIE = 0x80000033, // always has required bit?
    LC_DYLD_CHAINED_FIXUPS = 0x80000034,
}

/// Insertions (likely) should be divisible by 1000 to align with page boundaries
pub fn modify_offsets(buffer: &mut [u8], insertions: usize) -> Result<u32> {
    // Mach-O File Header
    let mut offset: usize = 0;

    // Lets parse functions know when to start modifying offsets
    // ie right after size change of __TEXT/__text
    let mut modify_offsets = false;

    const PRELUDE_SIZE: u32 = 32; // (in bytes)
    let mut args = ParserArgs {
        buffer,
        offset: &mut offset,
        insertions,
        com_size: PRELUDE_SIZE,
        modify_offsets: &mut modify_offsets,
    };

    let num_load = parse_prelude(&mut args);

    // Where to insert NOPs, specified by symtable after LC_SYMTAB
    let mut sym_insert_offset: Option<(u32, u32)> = None;

    // Where to insert NOPs, specified by size+offset of __text
    let mut text_insert_offset: Option<(u32, u32)> = None;

    for i in 0..num_load {
        println!("\nCommand #{i}");

        let com_type = read_u32_field(&buffer, &mut offset);
        println!("com_type: {com_type:x}");

        let com_size = read_u32_field(&buffer, &mut offset);
        println!("com_size: {com_size:x}");

        args = ParserArgs {
            buffer,
            offset: &mut offset,
            insertions,
            com_size: com_size - 8, // ignore com_type / com_size bytes
            modify_offsets: &mut modify_offsets,
        };

        // other commands unimplemented // TODO
        match com_type {
            0x2 => {
                let opt = parse_symtab(&mut args); // LC_SYMTAB
                if sym_insert_offset == None {
                    sym_insert_offset = opt;
                }
            },
            0x19 => {
                let opt = parse_segment(&mut args); // LC_SEGMENT_64
                if text_insert_offset == None {
                    text_insert_offset = opt;
                }
            },
            0x1D => parse_field1(&mut args), // remove_command(&mut args), // LC_CODE_SIGNATURE
            0x26 => parse_field1(&mut args), // LC_FUNCTION_STARTS // TODO modify function starts when past insert line
            0x80000028 => eat_bytes(&mut args), // LC_MAIN // DONT shift this one
            0x29 => parse_field1(&mut args), // LC_DATA_IN_CODE
            0x80000033 => remove_command(&mut args), // LC_DYLD_EXPORTS_TRIE
            0x80000034 => { // LC_DYLD_CHAINED_FIXUPS
                println!("removing LC_DYLD_CHAINED_FIXUPS");
                remove_command(&mut args);
            },
            _ => eat_bytes(&mut args), // other codes / error
        }
    }

    // valued first
    if let Some((offset, next_offset)) = sym_insert_offset {
        println!("text_offset: ({:x}, {:x})", offset, next_offset);
        parse_asm(buffer, offset, next_offset, insertions);
        return Ok(next_offset);
    }

    if let Some((offset, next_offset)) = text_insert_offset {
        println!("text_offset: ({:x}, {:x})", offset, next_offset);
        parse_asm(buffer, offset, next_offset, insertions);
        return Ok(next_offset);
    }

    Err(anyhow!("Insertion offset not found!"))
}

/// Parses from offset to next_offset, modifying
/// ASM commands where necessary, especially for
/// shifting relative instructions.
fn parse_asm(buffer: &mut [u8], offset: u32, next_offset: u32, insertions: usize) -> () {
    let mut index_offset: usize = offset.try_into().unwrap();
    while (index_offset as u32) < next_offset {
        let mut val = read_u32_field(buffer, &mut index_offset);

        // match ADR instruction
        if get_bit(val, 31) == 0 &&
           get_bit(val, 28) == 1 &&
           get_bit(val, 27) == 0 &&
           get_bit(val, 26) == 0 &&
           get_bit(val, 25) == 0 &&
           get_bit(val, 24) == 0 {
            println!("ADR instruction");
            // reconstruct address shift from immlo (bits 30-29) and immhi (bits 23-5)
            let mut imm: u32 = 0;
            for i in (5..23).rev() {
                imm = imm | (get_bit(val, i) << (i - 3));
                println!("{imm}");
            }
            for i in (29..30).rev() {
                imm = imm | (get_bit(val, i) << (i - 29));
            }

            let mut rd: u32 = 0;
            // contained in bits 4..0
            for i in (0..4).rev() {
                rd = rd | (get_bit(val, i) << i);
            }

            println!("imm: #0x{imm:x}, #{imm}");

            imm += (insertions * INSTR_LEN) as u32;

            println!("imm: #0x{imm:x}, #{imm}");

            // now deconstruct imm into val
            val = 0;
            val = val | (1 << 28);
            for i in (2..20).rev() {
                val = val | (get_bit(imm, i) << (i + 3));
            }
            for i in (0..1).rev() {
                val = val | (get_bit(imm, i) << (i + 29));
            }

            // rdister
            for i in (0..4).rev() {
                val = val | (get_bit(rd, i) << i);
            }

            println!("new val: {val}, {val:x}");

            index_offset -= 4;
            replace_u32_field(buffer, &index_offset, val);
            index_offset += 4;
        }
    }
}

/// Goes from LSB to MSB
/// Returns 0 or 1
fn get_bit(num: u32, bit_num: usize) -> u32 {
    assert!(bit_num < 32);

    let mask = 1 << bit_num;
    if mask & num > 0 {
        1
    } else {
        0
    }
}

/// Remove load command:
/// Shifts future commands over top,
/// Changes prelude information to match new num_com and com_size
/// Shifts offset *back* to correct position
fn remove_command(args: &mut ParserArgs) -> () {
    let (buffer, offset, _, com_size, _) = args.unwrap();

    // shift back to start of LC header
    let com_size_u8: usize = (com_size + 8).try_into().unwrap();
    *offset -= 8;

    const START_LCS: usize = 32;

    let mut temp_offset: usize = 16;
    let num_com: u32 = read_u32_field(buffer, &mut temp_offset);
    let load_size: usize = read_u32_field(buffer, &mut temp_offset).try_into().unwrap();

    let end_load: usize = START_LCS + load_size;

    // buffer[*offset..end_load-com_size_u8] = buffer[*offset+com_size_u8..end_load];
    for i in *offset..end_load-com_size_u8 {
        buffer[i] = buffer[i + com_size_u8];
    }

    let new_num_com = num_com - 1;
    let new_load_size = load_size as u32 - com_size - 8;

    temp_offset = 16; // move back to modify prelude fields
    replace_u32_field(buffer, &temp_offset, new_num_com);
    temp_offset += 4;
    replace_u32_field(buffer, &temp_offset, new_load_size);
    println!("REPLACED {load_size} with {new_load_size} at offset {temp_offset}");
}

/// Parses LC_SEGMENT_64 command
/// If __TEXT segment, returns *original* size+offset,
/// to know where to insert NOPs.
fn parse_segment(args: &mut ParserArgs) -> Option<(u32, u32)> {
    let (buffer, mut offset, insertions, _, modify_offsets) = args.unwrap();

    let mut ret_opt: Option<(u32, u32)> = None;

    // Parse load command
    // com_type and com_size already declared
    let seg_name = read_u128_name(&buffer, &mut offset);
    println!("seg_name: {seg_name}");

    let addr_offset = *offset;
    let mut addr = read_u64_field(&buffer, &mut offset);
    println!("addr: {addr:x}");

    let mut addr_size_offset = *offset;
    let mut addr_size = read_u64_field(&buffer, &mut offset);
    println!("addr_size: {addr_size:x}");

    let file_offset_offset = *offset;
    let mut file_offset = read_u64_field(&buffer, &mut offset);
    println!("file_offset: {file_offset:x}");

    if *modify_offsets {
        file_offset = file_offset + (insertions * INSTR_LEN) as u64;
        replace_u64_field(buffer, &file_offset_offset, file_offset);

        println!("NEW file_offset: {file_offset:x}");

        addr = addr + (insertions * INSTR_LEN) as u64;
        replace_u64_field(buffer, &addr_offset, addr);

        println!("NEW addr: {addr:x}");
    }

    let mut size_offset = *offset;
    let mut size = read_u64_field(&buffer, &mut offset);
    println!("size: {size:x}");

    // if this is __TEXT, file_offset will have NOT been modified yet
    let addr_plus_size: u32 = (file_offset + size).try_into().unwrap();

    let max_vm_protections = read_u32_field(&buffer, &mut offset);
    println!("max_vm_protections: {max_vm_protections:b}");

    let init_vm_protections = read_u32_field(&buffer, &mut offset);
    println!("init_vm_protections: {init_vm_protections:b}");

    let num_sections = read_u32_field(&buffer, &mut offset);
    println!("num_sections: {num_sections:x}");

    let flag32 = read_u32_field(&buffer, &mut offset);
    println!("flag32: {flag32:b}");

    for _ in 0..num_sections {
        println!("\n");
        let sec_name = read_u128_name(&buffer, &mut offset);
        println!("sec_name: {sec_name}");

        let seg_name = read_u128_name(&buffer, &mut offset);

        let mut sec_addr_offset = *offset;
        let mut sec_addr = read_u64_field(&buffer, &mut offset);
        println!("sec_addr: {sec_addr:x}");

        let mut sec_size_offset = *offset;
        let mut sec_size = read_u64_field(&buffer, &mut offset);
        println!("sec_size: {sec_size:x}");

        let mut sec_file_offset_offset = *offset;
        let mut sec_file_offset = read_u32_field(&buffer, &mut offset);
        println!("sec_file_offset: {sec_file_offset:x}");

        if *modify_offsets {
            sec_file_offset = sec_file_offset + (insertions * INSTR_LEN) as u32;
            replace_u32_field(buffer, &sec_file_offset_offset, sec_file_offset);

            sec_file_offset = read_u32_field(&buffer, &mut sec_file_offset_offset);

            println!("NEW sec_file_offset: {sec_file_offset:x}");

            sec_addr = sec_addr + (insertions * INSTR_LEN) as u64;
            replace_u64_field(buffer, &sec_addr_offset, sec_addr);

            sec_addr = read_u64_field(&buffer, &mut sec_addr_offset);

            println!("NEW sec_addr: {sec_addr:x}");
        }

        if seg_name.starts_with("__TEXT") && sec_name.starts_with("__text") {
            *modify_offsets = true;

            // change size of this *section*
            sec_size = sec_size + (insertions * INSTR_LEN) as u64;
            replace_u64_field(buffer, &sec_size_offset, sec_size);

            sec_size = read_u64_field(&buffer, &mut sec_size_offset);

            println!("NEW sec_size: {sec_size:x}");

            // change size of this *segment*

            println!("CHANGING SEG DETAILS");
            // assumes __TEXT is read first

            size = size + (insertions * INSTR_LEN) as u64;
            replace_u64_field(buffer, &size_offset, size);

            size = read_u64_field(&buffer, &mut size_offset);

            println!("NEW size: {size:x}");

            addr_size = addr_size + (insertions * INSTR_LEN) as u64;
            replace_u64_field(buffer, &addr_size_offset, addr_size);

            addr_size = read_u64_field(&buffer, &mut addr_size_offset);

            println!("NEW addr_size: {addr_size:x}");

            ret_opt = Some((file_offset.try_into().unwrap(), addr_plus_size));
        }

        let alignment = read_u32_field(&buffer, &mut offset);
        println!("alignment: {alignment:x}");

        let reloc_file_offset = read_u32_field(&buffer, &mut offset);
        println!("reloc_file_offset: {reloc_file_offset:x}");

        let num_relocations = read_u32_field(&buffer, &mut offset);
        println!("num_relocations: {num_relocations:x}");

        let flag_type = read_u32_field(&buffer, &mut offset);
        println!("flag_type: {flag_type:b}");

        let reserved1 = read_u32_field(&buffer, &mut offset);
        println!("reserved1: {reserved1:b}");

        let reserved2 = read_u32_field(&buffer, &mut offset);
        println!("reserved2: {reserved2:b}");

        let reserved3 = read_u32_field(&buffer, &mut offset);
        println!("reserved3: {reserved3:b}");
    }

    ret_opt
}

fn parse_prelude(args: &mut ParserArgs) -> u32 {
    let (buffer, offset, _, _, _) = args.unwrap();

    let magic_number = read_u32_field(&buffer, offset);
    println!("magic_number: {magic_number:x}");
    let cpu_type = read_u32_field(&buffer, offset);
    println!("cpu_type: {cpu_type:x}");
    let cpu_subtype = read_u32_field(&buffer, offset);
    println!("cpu_subtype: {cpu_subtype:x}");
    let file_type = read_u32_field(&buffer, offset);
    println!("file_type: {file_type:x}");
    let num_load = read_u32_field(&buffer, offset);
    println!("num_load: {num_load:x}");
    let size_load = read_u32_field(&buffer, offset);
    println!("size_load: {size_load:x}");
    let flags = read_u32_field(&buffer, offset);
    println!("flags: {flags:b}");
    let reserved = read_u32_field(&buffer, offset);
    println!("reserved: {reserved:x}");

    num_load
}

fn read_u32_field(buf: &[u8], offset: &mut usize) -> u32 {
    let val = reverse_endian_fb(buf[*offset..*offset+4].try_into().unwrap());
    *offset += 4;
    val
}

fn read_u64_field(buf: &[u8], offset: &mut usize) -> u64 {
    let val1 = reverse_endian_fb(buf[*offset..*offset+4].try_into().unwrap());
    let val2 = reverse_endian_fb(buf[*offset+4..*offset+8].try_into().unwrap());
    let val = ((val2 as u64) << 32) | (val1 as u64);
    *offset += 8;
    val
}

fn replace_u32_field(buf: &mut [u8], offset: &usize, value: u32) {
    let mut vec = to_bytes(value);
    vec.reverse();

    // insert back into buffer in order
    for i in *offset..*offset+4 {
        let index = i - (*offset);
        buf[i] = vec[index];
    }
}

fn replace_u64_field(buf: &mut [u8], offset: &usize, value: u64) {
    let val1 = (value >> 32) as u32;
    let val2 = (0x0000FFFF & value) as u32;

    let mut vec: Vec<u8> = Vec::new();
    vec.extend(to_bytes(val1));
    vec.extend(to_bytes(val2));
    vec.reverse();

    // insert back into buffer in order
    for i in *offset..*offset+8 {
        let index = i - (*offset);
        buf[i] = vec[index];
    }
}

fn read_u128_name(buf: &[u8], offset: &mut usize) -> String {
    let mut s = String::new();

    for i in 0..16 {
        s.push(buf[*offset + i] as char);
    }

    *offset += 16;
    s
}

/// Parses the LC_SYMTAB *instruction*
fn parse_symtab(args: &mut ParserArgs) -> Option<(u32, u32)> {
    let (buf, offset, insertions, _, _) = args.unwrap();

    // structure of command is: (i think)
    //   symoff  -  4 bytes
    //   nsyms   -  4 bytes
    //   stroff  -  4 bytes
    //   strsize -  4 bytes
    let add_val: u32 = (insertions * INSTR_LEN) as u32;

    let mut symoff_offset = *offset;
    let mut symoff = read_u32_field(&buf, offset);

    println!("symoff: {symoff:x}");

    symoff += add_val;
    replace_u32_field(buf, &symoff_offset, symoff);

    symoff = read_u32_field(&buf, &mut symoff_offset);

    println!("NEW symoff: {symoff:x}");

    let nsyms = read_u32_field(&buf, offset);

    println!("nsyms: {nsyms:x}");

    let mut stroff_offset = *offset;
    let mut stroff = read_u32_field(&buf, offset);

    println!("stroff: {stroff:x}");

    stroff += add_val;
    replace_u32_field(buf, &stroff_offset, stroff);

    stroff = read_u32_field(&buf, &mut stroff_offset);

    println!("NEW stroff: {stroff:x}");

    let strsize = read_u32_field(&buf, offset);

    println!("strsize: {strsize:x}");

    get_symbol_start(buf, symoff - add_val, nsyms, stroff - add_val, add_val)
}

/// Parses the real symbol table referenced by LC_SYMTAB,
/// to find _start symbol, returning offset if found.
/// Returns offset of _start symbol, and offset of symbol immediately following _start.
fn get_symbol_start(buffer: &mut [u8], symoff: u32, nsyms: u32, stroff: u32, add_val: u32) -> Option<(u32, u32)> {
    let mut symoff: usize = symoff.try_into().unwrap();
    let stroff: usize = stroff.try_into().unwrap();

    let mut entries: Vec<SymTableEntry> = Vec::new();

    let mut start_entry_opt: Option<SymTableEntry> = None;

    for _ in 0..nsyms {
        // read 16 (0x10) bytes
        let string_table_index = read_u32_field(buffer, &mut symoff);
        let mut sym_str_addr: u32 = (stroff as u32) + string_table_index;

        // eat_bytes(buf, symoff, 4); // reads type, sec index, description
        // eat_bytes(buf, symoff, 4); // reads first half of addr, we only care about 32-bit address
        symoff += 4;

        let sym_value = read_u32_field(buffer, &mut symoff);
        symoff += 4;

        // check if string table value points to _start string
        let str_table_value = read_zero_index_string(buffer, &mut sym_str_addr);

        entries.push(SymTableEntry {
            string_table_entry: str_table_value.clone(),
            value: sym_value,
            value_pointer: (symoff-8) as u32,
        });

        if str_table_value == "_start" {
            println!("FOUND _start");
            println!("sym_value: {sym_value:x}, {sym_value}");
            start_entry_opt = Some(entries[entries.len() - 1].clone());
        }
    }

    let start_entry;
    if let Some(entry) = start_entry_opt {
        start_entry = entry;
    }
    else {
        return None;
    }

    let mut smallest_val_after_start: Option<u32> = None;
    for entry in entries {
        if entry.value > start_entry.value {
            // add offset

            let change_offset: usize = entry.value_pointer.try_into().unwrap();
            replace_u32_field(buffer, &change_offset, entry.value + add_val);

            smallest_val_after_start = match smallest_val_after_start {
                None => Some(entry.value),
                Some(val) => {
                    if val > entry.value {
                        Some(entry.value)
                    } else {
                        Some(val)
                    }
                },
            }
        }
    }

    if let Some(val) = smallest_val_after_start {
        Some((start_entry.value, val))
    } else {
        None
    }
}

#[derive(Clone)]
struct SymTableEntry {
    string_table_entry: String,
    value: u32,
    value_pointer: u32,
}

fn read_zero_index_string(buffer: &[u8], offset: &mut u32) -> String {
    let mut ret_str = String::new();

    let mut index: usize = (*offset).try_into().unwrap();

    while buffer[index] != 0x00 {
        ret_str.push(buffer[index] as char);
        *offset += 1;
        index = (*offset).try_into().unwrap();
    }

    ret_str
}

fn parse_field1(args: &mut ParserArgs) {
    let (buf, offset, insertions, _, _) = args.unwrap();

    // assuming first is u64 field
    let mut val_offset: usize = *offset;
    let mut val = read_u64_field(buf, offset);

    println!("field1: {val:x}, {val}, {val:b}");

    val = val + (insertions * INSTR_LEN) as u64;
    replace_u64_field(buf, &val_offset, val);

    val = read_u64_field(&buf, &mut val_offset);

    println!("NEW field1: {val:x}, {val:b}");

    args.com_size -= 8; // changing this value doesn't matter outside of func call
    eat_bytes(args);
}

fn eat_bytes(args: &mut ParserArgs) {
    let (buf, offset, _, com_size, _) = args.unwrap();

    // assuming all u32 fields
    for _ in 0..(com_size / 4) {
        let val = read_u32_field(buf, offset);

        println!("_: {val:x}, {val}, {val:b}");
    }
}
